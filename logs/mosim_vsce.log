[08-04 13:39:10.913] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 13:39:13.075] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:39:13.232] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:39:13.342] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:39:15.088] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:39:19.285] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:42:12.084] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[08-04 13:45:14.134] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 13:45:15.890] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:45:16.034] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:45:16.139] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:46:51.621] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[08-04 13:46:53.342] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:46:53.705] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:46:53.740] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:46:58.404] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:46:58.534] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:46:58.704] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:48:33.107] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[08-04 13:48:38.354] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:48:38.524] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:48:38.638] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:49:15.021] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[08-04 13:49:16.924] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:49:17.055] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:49:17.193] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:49:23.389] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 13:49:23.538] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 13:49:23.653] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 13:52:15.128] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[08-04 14:03:14.086] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 14:03:26.226] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 14:03:27.920] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 14:03:29.177] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 14:03:29.180] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 14:03:38.876] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[08-04 16:29:32.724] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 16:29:34.999] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 16:29:35.054] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:29:35.114] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 16:30:03.009] [info] work/n900_vnice_npu_soc {"type":"command","command":"monitor","payload":{"type":"st_j","cli":"st_j n900_vnice"}}
[08-04 16:30:18.984] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:26.277] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:28.001] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[08-04 16:30:30.151] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 16:30:30.301] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:30.480] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 16:30:32.003] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:35.525] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:45.854] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:30:47.345] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[08-04 16:31:09.301] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 16:31:11.397] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 16:31:11.543] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:31:11.728] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[08-04 16:31:13.096] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:31:50.070] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[08-04 16:31:51.544] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[08-04 16:33:02.785] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 16:33:08.998] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"./run_with_log.sh","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[08-04 16:33:10.384] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[08-04 16:34:08.301] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[08-04 16:34:10.088] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[08-04 16:34:16.430] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}