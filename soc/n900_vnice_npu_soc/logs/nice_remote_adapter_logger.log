[08-08 17:13:05.825] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-08 17:13:52.062] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.062] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.063] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 301982
[08-08 17:13:52.063] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.063] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.063] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.063] [603964 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.065] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.065] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.066] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 301993
[08-08 17:13:52.067] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.067] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.067] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.067] [603986 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.068] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.068] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.069] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302004
[08-08 17:13:52.069] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.069] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.069] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.069] [604008 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302015
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.071] [604030 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302026
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.073] [604052 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.075] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.075] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.076] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302037
[08-08 17:13:52.076] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.076] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.076] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.076] [604074 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 302083
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.083] [604166 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.085] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.085] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.086] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302094
[08-08 17:13:52.086] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.086] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.086] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.086] [604188 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.088] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.088] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.088] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302105
[08-08 17:13:52.089] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.089] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.089] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.089] [604210 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.090] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.090] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.091] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302116
[08-08 17:13:52.091] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.091] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.091] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.091] [604232 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.093] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.093] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.094] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302127
[08-08 17:13:52.094] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.094] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.094] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.094] [604254 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302138
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.096] [604276 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.102] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.102] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.103] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 302174
[08-08 17:13:52.103] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.103] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.103] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.103] [604348 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.105] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.105] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.106] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302185
[08-08 17:13:52.106] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.106] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.106] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.106] [604370 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302196
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.108] [604392 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.110] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.110] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.111] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302207
[08-08 17:13:52.111] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.111] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.111] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.111] [604414 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302218
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.113] [604436 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.115] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.115] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.115] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302229
[08-08 17:13:52.116] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.116] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.116] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.116] [604458 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.121] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.121] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.122] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 302265
[08-08 17:13:52.122] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.122] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.122] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.122] [604530 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.124] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.124] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.124] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302276
[08-08 17:13:52.125] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.125] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.125] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.125] [604552 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.126] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.126] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.127] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302287
[08-08 17:13:52.127] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.127] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.127] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.127] [604574 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.129] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.129] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.129] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302298
[08-08 17:13:52.130] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.130] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.130] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.130] [604596 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.131] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.131] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.132] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302309
[08-08 17:13:52.132] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.132] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.132] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.132] [604618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302320
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.134] [604640 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.210] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.210] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.211] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 302828
[08-08 17:13:52.211] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.211] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.211] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.211] [605656 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.212] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.212] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.213] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302839
[08-08 17:13:52.213] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.213] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.213] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.213] [605678 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302850
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.215] [605700 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302861
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.217] [605722 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.219] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.219] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.219] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302872
[08-08 17:13:52.220] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.220] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.220] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.220] [605744 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.221] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.221] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.222] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302883
[08-08 17:13:52.222] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.222] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.222] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.222] [605766 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.227] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.227] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.228] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 302919
[08-08 17:13:52.228] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.228] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.228] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.228] [605838 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.230] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.230] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.230] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302930
[08-08 17:13:52.231] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.231] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.231] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.231] [605860 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.232] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.232] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.233] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302941
[08-08 17:13:52.233] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.233] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.233] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.233] [605882 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302952
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.235] [605904 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.237] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.237] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.237] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302963
[08-08 17:13:52.238] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.238] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.238] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.238] [605926 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.239] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.239] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.240] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 302974
[08-08 17:13:52.240] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.240] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.240] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.240] [605948 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 303010
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.246] [606020 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.248] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.248] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.249] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303021
[08-08 17:13:52.249] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.249] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.249] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.249] [606042 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303032
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.251] [606064 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.253] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.253] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.253] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303043
[08-08 17:13:52.254] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.254] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.254] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.254] [606086 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.255] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.255] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.256] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303054
[08-08 17:13:52.256] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.256] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.256] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.256] [606108 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303065
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.258] [606130 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 303101
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.264] [606202 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.266] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.266] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.267] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303112
[08-08 17:13:52.267] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.267] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.267] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.267] [606224 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303123
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.269] [606246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.271] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.271] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.271] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303134
[08-08 17:13:52.272] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.272] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.272] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.272] [606268 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.273] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.273] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.274] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303145
[08-08 17:13:52.274] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.274] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.274] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.274] [606290 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 303156
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:52.276] [606312 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:57.078] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:57.078] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:57.079] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 333700
[08-08 17:13:57.079] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:57.079] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:57.079] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:57.079] [667400 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:57.082] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:57.082] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:57.083] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 333713
[08-08 17:13:57.083] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:57.083] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:57.083] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:57.083] [667426 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:57.711] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:57.711] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:57.713] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 337724
[08-08 17:13:57.713] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:57.713] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:57.713] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:57.713] [675448 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:57.715] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:57.715] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:57.716] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 337737
[08-08 17:13:57.716] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:57.716] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:57.716] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:57.716] [675474 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:59.001] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:59.001] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:59.006] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 345950
[08-08 17:13:59.006] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:59.006] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:59.006] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:59.006] [691900 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:59.009] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:59.009] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:59.010] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 345963
[08-08 17:13:59.010] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:59.010] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:59.010] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:59.010] [691926 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 346133
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:59.038] [692266 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 346144
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:13:59.040] [692288 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:03.715] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:03.715] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:03.718] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 376529
[08-08 17:14:03.718] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:03.718] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:03.718] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:03.718] [753058 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:03.720] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:03.720] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:03.721] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 376542
[08-08 17:14:03.721] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:03.722] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:03.722] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:03.722] [753084 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:04.336] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:04.336] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:04.339] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 380494
[08-08 17:14:04.339] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:04.339] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:04.339] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:04.339] [760988 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 380507
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:04.341] [761014 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:05.576] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:05.576] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:05.578] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 388700
[08-08 17:14:05.578] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:05.578] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:05.578] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:05.578] [777400 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:05.581] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:05.581] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:05.582] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 388713
[08-08 17:14:05.582] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:05.582] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:05.582] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:05.582] [777426 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:05.614] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:05.614] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:05.615] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 388852
[08-08 17:14:05.615] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:05.615] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:05.615] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:05.615] [777704 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 388863
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:05.617] [777726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:10.165] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:10.165] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:10.166] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 419074
[08-08 17:14:10.166] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:10.166] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:10.166] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:10.166] [838148 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 419087
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:10.168] [838174 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:10.765] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:10.765] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:10.767] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 423030
[08-08 17:14:10.767] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:10.767] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:10.767] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:10.767] [846060 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 423043
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:10.769] [846086 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:11.971] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:11.971] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:11.971] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 431130
[08-08 17:14:11.972] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:11.972] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:11.972] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:11.972] [862260 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 431143
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:11.974] [862286 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 431282
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:11.994] [862564 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 431293
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:11.996] [862586 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:16.414] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:16.414] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:16.416] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 461391
[08-08 17:14:16.416] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:16.416] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:16.416] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:16.416] [922782 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 461404
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:16.418] [922808 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:17.011] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:17.011] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:17.013] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 465348
[08-08 17:14:17.013] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:17.013] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:17.013] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:17.013] [930696 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:17.015] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:17.015] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:17.016] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 465361
[08-08 17:14:17.016] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:17.016] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:17.016] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:17.016] [930722 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:18.201] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:18.201] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:18.202] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 473491
[08-08 17:14:18.202] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:18.202] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:18.202] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:18.202] [946982 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 473504
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:18.204] [947008 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:18.223] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:18.223] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:18.224] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 473643
[08-08 17:14:18.224] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:18.224] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:18.224] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:18.224] [947286 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:18.225] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:18.225] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:18.226] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 473654
[08-08 17:14:18.226] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:18.226] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:18.226] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:18.226] [947308 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:22.631] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:22.631] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:22.632] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 503698
[08-08 17:14:22.632] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:22.632] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:22.632] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:22.632] [1007396 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:22.634] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:22.634] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:22.634] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 503711
[08-08 17:14:22.635] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:22.635] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:22.635] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:22.635] [1007422 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:23.261] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:23.261] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:23.263] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 507662
[08-08 17:14:23.263] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:23.263] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:23.263] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:23.263] [1015324 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 507675
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:23.265] [1015350 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:24.525] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:24.525] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:24.527] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 515754
[08-08 17:14:24.528] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:24.528] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:24.528] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:24.528] [1031508 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:24.530] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:24.530] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:24.531] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 515767
[08-08 17:14:24.531] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:24.531] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:24.531] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:24.531] [1031534 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:24.553] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:24.553] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:24.554] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 515906
[08-08 17:14:24.554] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:24.554] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:24.554] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:24.554] [1031812 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:24.556] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:24.556] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:24.557] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 515917
[08-08 17:14:24.557] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:24.557] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:24.557] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:24.557] [1031834 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:29.184] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:29.184] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:29.186] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 545939
[08-08 17:14:29.187] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:29.187] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:29.187] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:29.187] [1091878 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:29.189] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:29.189] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:29.190] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 545952
[08-08 17:14:29.190] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:29.190] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:29.190] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:29.190] [1091904 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:29.794] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:29.794] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:29.801] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 549894
[08-08 17:14:29.801] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:29.801] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:29.801] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:29.801] [1099788 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:29.803] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:29.803] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:29.804] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 549907
[08-08 17:14:29.804] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:29.804] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:29.804] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:29.804] [1099814 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:31.031] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:31.031] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:31.033] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 557992
[08-08 17:14:31.034] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:31.034] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:31.034] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:31.034] [1115984 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:31.036] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:31.036] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:31.037] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 558005
[08-08 17:14:31.037] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:31.037] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:31.037] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:31.037] [1116010 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:31.054] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:31.054] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:31.055] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 558120
[08-08 17:14:31.055] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:31.055] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:31.055] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:31.055] [1116240 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:31.057] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:31.057] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:31.058] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 558131
[08-08 17:14:31.058] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:31.058] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:31.058] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:31.058] [1116262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:35.622] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:35.622] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:35.623] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 588110
[08-08 17:14:35.624] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:35.624] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:35.624] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:35.624] [1176220 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 588123
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:35.626] [1176246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:36.213] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:36.213] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:36.219] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 592032
[08-08 17:14:36.219] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:36.219] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:36.219] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:36.219] [1184064 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:36.221] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:36.221] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:36.222] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 592045
[08-08 17:14:36.222] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:36.222] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:36.222] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:36.222] [1184090 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:37.444] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:37.444] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:37.446] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 600074
[08-08 17:14:37.447] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:37.447] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:37.447] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:37.447] [1200148 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:37.449] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:37.449] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:37.450] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 600087
[08-08 17:14:37.450] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:37.450] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:37.450] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:37.450] [1200174 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:37.468] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:37.468] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:37.469] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 600202
[08-08 17:14:37.469] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:37.469] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:37.469] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:37.469] [1200404 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:37.471] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:37.471] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:37.472] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 600213
[08-08 17:14:37.472] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:37.472] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:37.472] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:37.472] [1200426 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:42.098] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:42.098] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:42.101] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 630346
[08-08 17:14:42.101] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:42.101] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:42.101] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:42.101] [1260692 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:42.103] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:42.103] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:42.104] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 630359
[08-08 17:14:42.104] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:42.104] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:42.104] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:42.104] [1260718 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:42.689] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:42.689] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:42.694] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 634319
[08-08 17:14:42.695] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:42.695] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:42.695] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:42.695] [1268638 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:42.697] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:42.697] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:42.698] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 634332
[08-08 17:14:42.698] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:42.698] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:42.698] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:42.698] [1268664 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:43.965] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:43.965] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:43.968] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 642481
[08-08 17:14:43.968] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:43.968] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:43.968] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:43.968] [1284962 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:43.970] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:43.970] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:43.972] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 642494
[08-08 17:14:43.972] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:43.972] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:43.972] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:43.972] [1284988 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:43.997] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:43.997] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:43.998] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 642652
[08-08 17:14:43.998] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:43.998] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:43.998] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:43.998] [1285304 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:44.000] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:44.000] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:44.000] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 642663
[08-08 17:14:44.000] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:44.001] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:44.001] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:44.001] [1285326 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:48.657] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:48.657] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:48.660] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 672957
[08-08 17:14:48.660] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:48.660] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:48.660] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:48.660] [1345914 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:48.662] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:48.662] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:48.664] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 672970
[08-08 17:14:48.664] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:48.664] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:48.664] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:48.664] [1345940 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:49.266] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:49.266] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:49.270] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 676932
[08-08 17:14:49.271] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:49.271] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:49.271] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:49.271] [1353864 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 676945
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:49.273] [1353890 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:50.507] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:50.507] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:50.509] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 685028
[08-08 17:14:50.509] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:50.509] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:50.509] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:50.509] [1370056 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:50.511] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:50.511] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:50.512] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 685041
[08-08 17:14:50.513] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:50.513] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:50.513] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:50.513] [1370082 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 685190
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:50.535] [1370380 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 685201
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:50.537] [1370402 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:55.146] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:55.146] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:55.148] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 715582
[08-08 17:14:55.149] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:55.149] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:55.149] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:55.149] [1431164 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:55.151] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:55.151] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:55.152] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 715595
[08-08 17:14:55.152] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:55.152] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:55.152] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:55.152] [1431190 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:55.772] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:55.772] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:55.777] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 719557
[08-08 17:14:55.777] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:55.777] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:55.777] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:55.777] [1439114 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:55.779] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:55.779] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:55.780] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 719570
[08-08 17:14:55.781] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:55.781] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:55.781] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:55.781] [1439140 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:57.017] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:57.017] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:57.020] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 727676
[08-08 17:14:57.020] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:57.020] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:57.020] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:57.020] [1455352 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:57.022] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:57.022] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:57.023] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 727689
[08-08 17:14:57.023] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:57.023] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:57.023] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:57.023] [1455378 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:57.041] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:57.041] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:57.042] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 727804
[08-08 17:14:57.042] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:57.042] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:57.042] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:57.042] [1455608 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:14:57.044] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:14:57.044] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:14:57.044] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 727815
[08-08 17:14:57.045] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:14:57.045] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:14:57.045] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:14:57.045] [1455630 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:01.774] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:01.774] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:01.776] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 758522
[08-08 17:15:01.776] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:01.776] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:01.776] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:01.776] [1517044 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:01.778] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:01.778] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:01.781] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 758535
[08-08 17:15:01.781] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:01.781] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:01.781] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:01.781] [1517070 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:02.393] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:02.393] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:02.398] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 762463
[08-08 17:15:02.399] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:02.399] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:02.399] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:02.399] [1524926 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:02.401] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:02.401] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:02.402] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 762476
[08-08 17:15:02.402] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:02.402] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:02.402] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:02.402] [1524952 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:03.640] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:03.640] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:03.642] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 770541
[08-08 17:15:03.642] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:03.642] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:03.642] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:03.642] [1541082 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:03.644] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:03.644] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:03.645] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 770554
[08-08 17:15:03.646] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:03.646] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:03.646] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:03.646] [1541108 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:03.663] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 770669
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:03.664] [1541338 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:03.666] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:03.666] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:03.667] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 770680
[08-08 17:15:03.667] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:03.667] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:03.667] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:03.667] [1541360 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:08.375] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:08.375] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:08.377] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 801498
[08-08 17:15:08.377] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:08.377] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:08.377] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:08.377] [1602996 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:08.379] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:08.379] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:08.380] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 801511
[08-08 17:15:08.380] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:08.380] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:08.380] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:08.380] [1603022 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:08.984] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:08.984] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:08.988] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 805415
[08-08 17:15:08.989] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:08.989] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:08.989] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:08.989] [1610830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:08.991] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:08.991] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:08.992] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 805428
[08-08 17:15:08.992] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:08.992] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:08.992] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:08.992] [1610856 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:10.220] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:10.221] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:10.222] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 813526
[08-08 17:15:10.222] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:10.222] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:10.222] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:10.222] [1627052 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 813539
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:10.225] [1627078 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 813678
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:10.247] [1627356 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:10.249] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:10.249] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:10.250] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 813689
[08-08 17:15:10.250] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:10.250] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:10.250] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:10.250] [1627378 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:14.953] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:14.953] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:14.955] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 844498
[08-08 17:15:14.956] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:14.956] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:14.956] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:14.956] [1688996 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:14.958] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:14.958] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:14.959] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 844511
[08-08 17:15:14.959] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:14.959] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:14.959] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:14.959] [1689022 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:15.563] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:15.563] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:15.568] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 848464
[08-08 17:15:15.569] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:15.569] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:15.569] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:15.569] [1696928 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:15.571] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:15.571] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:15.572] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 848477
[08-08 17:15:15.572] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:15.572] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:15.572] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:15.572] [1696954 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:16.835] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:16.835] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:16.838] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 856534
[08-08 17:15:16.838] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:16.838] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:16.838] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:16.838] [1713068 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:16.841] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:16.841] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:16.842] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 856547
[08-08 17:15:16.842] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:16.842] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:16.842] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:16.842] [1713094 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:16.863] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:16.863] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:16.864] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 856686
[08-08 17:15:16.864] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:16.864] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:16.864] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:16.864] [1713372 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 856697
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:16.866] [1713394 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:21.550] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:21.550] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:21.553] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 887425
[08-08 17:15:21.553] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:21.553] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:21.553] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:21.553] [1774850 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:21.555] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:21.555] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:21.557] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 887438
[08-08 17:15:21.557] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:21.557] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:21.557] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:21.557] [1774876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:22.157] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:22.157] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:22.162] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 891391
[08-08 17:15:22.163] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:22.163] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:22.163] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:22.163] [1782782 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:22.165] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:22.165] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:22.166] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 891404
[08-08 17:15:22.166] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:22.166] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:22.166] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:22.166] [1782808 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:23.389] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:23.389] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:23.391] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 899498
[08-08 17:15:23.391] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:23.391] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:23.391] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:23.391] [1798996 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:23.394] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:23.394] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:23.395] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 899511
[08-08 17:15:23.395] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:23.395] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:23.395] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:23.395] [1799022 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:23.413] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:23.413] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:23.413] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 899627
[08-08 17:15:23.414] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:23.414] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:23.414] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:23.414] [1799254 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:23.415] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:23.415] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:23.416] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 899638
[08-08 17:15:23.417] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:23.417] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:23.417] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:23.417] [1799276 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:28.081] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:28.081] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:28.084] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 930271
[08-08 17:15:28.085] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:28.085] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:28.085] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:28.085] [1860542 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:28.087] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:28.087] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:28.088] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 930284
[08-08 17:15:28.088] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:28.088] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:28.088] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:28.088] [1860568 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:28.698] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:28.698] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:28.701] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 934215
[08-08 17:15:28.701] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:28.701] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:28.701] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:28.701] [1868430 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 934228
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:28.704] [1868456 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:29.916] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:29.916] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:29.918] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 942281
[08-08 17:15:29.919] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:29.919] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:29.919] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:29.919] [1884562 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:29.921] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:29.921] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:29.922] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 942294
[08-08 17:15:29.922] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:29.922] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:29.922] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:29.922] [1884588 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:29.940] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:29.940] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:29.940] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 942410
[08-08 17:15:29.941] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:29.941] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:29.941] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:29.941] [1884820 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:29.942] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:29.942] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:29.943] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 942421
[08-08 17:15:29.943] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:29.943] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:29.943] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:29.943] [1884842 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:34.614] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:34.614] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:34.617] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 972949
[08-08 17:15:34.617] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:34.617] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:34.617] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:34.617] [1945898 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:34.619] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:34.619] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:34.620] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 972962
[08-08 17:15:34.620] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:34.620] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:34.620] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:34.620] [1945924 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:35.217] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:35.217] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:35.223] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 976824
[08-08 17:15:35.223] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:35.223] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:35.223] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:35.223] [1953648 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 976837
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:35.226] [1953674 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:36.462] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:36.462] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:36.464] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 984916
[08-08 17:15:36.464] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:36.464] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:36.464] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:36.464] [1969832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:36.467] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:36.467] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:36.467] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 984929
[08-08 17:15:36.468] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:36.468] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:36.468] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:36.468] [1969858 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:36.485] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:36.485] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:36.487] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 985045
[08-08 17:15:36.487] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:36.487] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:36.487] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:36.487] [1970090 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:36.489] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:36.489] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:36.490] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 985056
[08-08 17:15:36.490] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:36.490] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:36.490] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:36.490] [1970112 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:41.180] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:41.180] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:41.182] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1015377
[08-08 17:15:41.183] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:41.183] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:41.183] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:41.183] [2030754 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:41.185] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:41.185] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:41.186] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1015390
[08-08 17:15:41.186] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:41.186] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:41.186] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:41.186] [2030780 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:41.800] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:41.800] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:41.805] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1019317
[08-08 17:15:41.805] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:41.805] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:41.805] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:41.805] [2038634 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:41.808] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:41.808] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:41.808] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1019330
[08-08 17:15:41.809] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:41.809] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:41.809] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:41.809] [2038660 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:43.040] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:43.040] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:43.042] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1027409
[08-08 17:15:43.043] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:43.043] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:43.043] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:43.043] [2054818 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:43.045] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:43.045] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:43.046] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1027422
[08-08 17:15:43.046] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:43.046] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:43.046] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:43.046] [2054844 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:43.064] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:43.064] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:43.065] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1027538
[08-08 17:15:43.065] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:43.065] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:43.065] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:43.065] [2055076 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:43.067] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:43.067] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:43.067] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1027549
[08-08 17:15:43.068] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:43.068] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:43.068] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:43.068] [2055098 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:47.688] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:47.688] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:47.691] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1057580
[08-08 17:15:47.691] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:47.691] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:47.691] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:47.691] [2115160 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:47.693] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:47.693] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:47.694] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1057593
[08-08 17:15:47.695] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:47.695] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:47.695] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:47.695] [2115186 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:48.305] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:48.305] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:48.310] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1061506
[08-08 17:15:48.310] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:48.310] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:48.310] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:48.310] [2123012 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1061519
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:48.313] [2123038 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:49.549] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:49.549] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:49.551] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1069553
[08-08 17:15:49.552] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:49.552] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:49.552] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:49.552] [2139106 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:49.554] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:49.554] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:49.555] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1069566
[08-08 17:15:49.555] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:49.555] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:49.555] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:49.555] [2139132 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:49.573] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:49.573] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:49.575] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1069681
[08-08 17:15:49.575] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:49.575] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:49.575] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:49.575] [2139362 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:49.576] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:49.576] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:49.577] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1069692
[08-08 17:15:49.577] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:49.577] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:49.577] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:49.577] [2139384 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:54.034] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:54.034] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:54.036] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1099710
[08-08 17:15:54.037] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:54.037] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:54.037] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:54.037] [2199420 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1099723
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:54.039] [2199446 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:54.610] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:54.610] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:54.615] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1103625
[08-08 17:15:54.615] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:54.615] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:54.615] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:54.615] [2207250 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:54.617] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:54.617] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:54.618] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1103638
[08-08 17:15:54.618] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:54.618] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:54.618] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:54.618] [2207276 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:55.793] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:55.793] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:55.795] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1111719
[08-08 17:15:55.795] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:55.795] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:55.795] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:55.795] [2223438 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:55.797] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:55.797] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:55.798] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1111732
[08-08 17:15:55.798] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:55.798] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:55.798] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:55.798] [2223464 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:55.815] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:55.815] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:55.816] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1111847
[08-08 17:15:55.817] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:55.817] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:55.817] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:55.817] [2223694 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:15:55.818] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:15:55.818] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:15:55.819] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1111858
[08-08 17:15:55.819] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:15:55.819] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:15:55.819] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:15:55.819] [2223716 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:00.215] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:00.215] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:00.216] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1141857
[08-08 17:16:00.216] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:00.216] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:00.216] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:00.216] [2283714 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:00.218] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:00.218] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:00.219] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1141870
[08-08 17:16:00.219] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:00.219] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:00.219] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:00.219] [2283740 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:00.796] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:00.796] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:00.801] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1145795
[08-08 17:16:00.802] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:00.802] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:00.802] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:00.802] [2291590 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:00.804] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:00.804] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:00.805] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1145808
[08-08 17:16:00.805] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:00.805] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:00.805] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:00.805] [2291616 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:01.981] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:01.981] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:01.983] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1153848
[08-08 17:16:01.983] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:01.983] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:01.983] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:01.983] [2307696 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:01.985] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:01.985] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:01.987] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1153861
[08-08 17:16:01.987] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:01.987] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:01.987] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:01.987] [2307722 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:02.004] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:02.004] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:02.005] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1153977
[08-08 17:16:02.005] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:02.005] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:02.005] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:02.005] [2307954 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:02.006] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:02.006] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:02.007] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1153988
[08-08 17:16:02.008] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:02.008] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:02.008] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:02.008] [2307976 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:06.425] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:06.425] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:06.427] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1183967
[08-08 17:16:06.428] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:06.428] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:06.428] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:06.428] [2367934 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:06.430] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:06.430] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:06.430] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1183980
[08-08 17:16:06.431] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:06.431] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:06.431] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:06.431] [2367960 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:07.013] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:07.013] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:07.019] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1187892
[08-08 17:16:07.019] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:07.019] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:07.019] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:07.019] [2375784 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1187905
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:07.022] [2375810 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:08.216] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:08.216] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:08.218] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1195990
[08-08 17:16:08.218] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:08.218] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:08.218] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:08.218] [2391980 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:08.220] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:08.220] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:08.221] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1196003
[08-08 17:16:08.222] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:08.222] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:08.222] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:08.222] [2392006 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:08.239] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:08.239] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:08.240] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1196119
[08-08 17:16:08.240] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:08.240] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:08.240] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:08.240] [2392238 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:08.242] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:08.242] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:08.243] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1196130
[08-08 17:16:08.243] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:08.243] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:08.243] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:08.243] [2392260 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:12.651] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:12.651] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:12.653] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1226110
[08-08 17:16:12.653] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:12.653] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:12.653] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:12.653] [2452220 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:12.655] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:12.655] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:12.656] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 1226123
[08-08 17:16:12.656] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:12.656] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:12.656] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:12.656] [2452246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:13.243] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:13.243] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:13.249] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1230025
[08-08 17:16:13.249] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:13.249] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:13.249] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:13.249] [2460050 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:13.251] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:13.251] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:13.252] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 1230038
[08-08 17:16:13.252] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:13.252] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:13.252] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:13.252] [2460076 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:14.468] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:14.468] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:14.470] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1238067
[08-08 17:16:14.471] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:14.471] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:14.471] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:14.471] [2476134 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:14.473] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:14.473] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:14.474] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 1238080
[08-08 17:16:14.474] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:14.474] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:14.474] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:14.474] [2476160 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:14.491] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:14.491] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:14.492] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 1238195
[08-08 17:16:14.492] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:14.492] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:14.492] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:14.492] [2476390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-08 17:16:14.494] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-08 17:16:14.494] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-08 17:16:14.495] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 1238206
[08-08 17:16:14.495] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-08 17:16:14.495] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-08 17:16:14.495] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-08 17:16:14.495] [2476412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
