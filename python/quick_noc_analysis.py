#!/usr/bin/env python3
"""
快速NOC和VVV操作分析工具
专门针对当前的日志格式进行快速分析
"""

import re
from pathlib import Path
from collections import defaultdict, Counter

def parse_coordinate(coord_hex: str) -> tuple:
    """解析16进制坐标为(group_id, core_id)"""
    coord = int(coord_hex, 16)
    group_id = (coord >> 8) & 0xFF
    core_id = coord & 0xFF
    return group_id, core_id

def parse_address_space(addr: int) -> str:
    """识别地址空间"""
    if addr == 0x00000000:
        return "SPAD0_BASE"
    elif addr == 0x00100000:
        return "SPAD1_BASE"
    elif addr == 0x00200000:
        return "SPAD2_BASE"
    elif addr == 0x00300000:
        return "SPAD3_BASE"
    elif 0x00001000 <= addr <= 0x00001FFF:
        return "SPAD0_INTER"
    elif 0x00101000 <= addr <= 0x00101FFF:
        return "SPAD1_INTER"
    elif addr & 0xFF000000 == 0x00000000:
        offset = addr & 0xFFFFFF
        if offset % 0x200 == 0:
            row = offset // 0x200
            return f"SPAD0_ROW{row}"
        else:
            return f"SPAD0_OFFSET{offset:x}"
    elif addr & 0xFF000000 == 0x00100000:
        offset = addr & 0xFFFFFF
        if offset % 0x200 == 0:
            row = offset // 0x200
            return f"SPAD1_ROW{row}"
        else:
            return f"SPAD1_OFFSET{offset:x}"
    else:
        return f"UNKNOWN_{addr:08x}"

def analyze_noc_vvv_log(log_file: str, output_file: str = "noc_analysis.log"):
    """分析NOC和VVV操作日志"""
    
    print(f"📖 分析日志文件: {log_file}")
    
    # 正则表达式模式
    noc_src_pattern = re.compile(r'handle noc_src_drv\(([^,]+), (\d+), (\d+)\)')
    noc_dest_pattern = re.compile(r'handle noc_dest_drv\(([^,]+), (\d+), (\d+)\)')
    vvv_pattern = re.compile(r'handle vvv_drv\(([^,]+), (\d+), (\d+)\)')
    mask_pattern = re.compile(r'Group:(\d+) - Mask:\[(True|False), (True|False), (True|False), (True|False)\]')
    
    # 存储解析结果
    noc_operations = []
    vvv_operations = []
    
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 解析NOC src操作
        noc_src_match = noc_src_pattern.search(line)
        if noc_src_match:
            instruction, address, dest_coord = noc_src_match.groups()
            
            # 解析mask信息
            masks = {}
            for j in range(i+1, min(i+5, len(lines))):
                mask_match = mask_pattern.search(lines[j])
                if mask_match:
                    group_id = int(mask_match.group(1))
                    mask_values = [mask_match.group(k) == 'True' for k in range(2, 6)]
                    masks[group_id] = any(mask_values)
            
            dest_group, dest_core = parse_coordinate(dest_coord)
            addr_space = parse_address_space(int(address))
            
            noc_operations.append({
                'type': 'src_drv',
                'instruction': instruction,
                'address': int(address),
                'address_hex': f"0x{int(address):08x}",
                'address_space': addr_space,
                'dest_coord': dest_coord,
                'dest_group': dest_group,
                'dest_core': dest_core,
                'masks': masks,
                'line_num': i+1
            })
            i += 5
            continue
        
        # 解析NOC dest操作
        noc_dest_match = noc_dest_pattern.search(line)
        if noc_dest_match:
            instruction, address, src_coord = noc_dest_match.groups()
            
            # 解析mask信息
            masks = {}
            for j in range(i+1, min(i+5, len(lines))):
                mask_match = mask_pattern.search(lines[j])
                if mask_match:
                    group_id = int(mask_match.group(1))
                    mask_values = [mask_match.group(k) == 'True' for k in range(2, 6)]
                    masks[group_id] = any(mask_values)
            
            src_group, src_core = parse_coordinate(src_coord)
            addr_space = parse_address_space(int(address))
            
            noc_operations.append({
                'type': 'dest_drv',
                'instruction': instruction,
                'address': int(address),
                'address_hex': f"0x{int(address):08x}",
                'address_space': addr_space,
                'src_coord': src_coord,
                'src_group': src_group,
                'src_core': src_core,
                'masks': masks,
                'line_num': i+1
            })
            i += 5
            continue
        
        # 解析VVV操作
        vvv_match = vvv_pattern.search(line)
        if vvv_match:
            instruction, input_addr, output_addr = vvv_match.groups()
            
            # 解析mask信息
            masks = {}
            for j in range(i+1, min(i+5, len(lines))):
                mask_match = mask_pattern.search(lines[j])
                if mask_match:
                    group_id = int(mask_match.group(1))
                    mask_values = [mask_match.group(k) == 'True' for k in range(2, 6)]
                    masks[group_id] = any(mask_values)
            
            input_space = parse_address_space(int(input_addr))
            output_space = parse_address_space(int(output_addr))
            
            vvv_operations.append({
                'instruction': instruction,
                'input_addr': int(input_addr),
                'input_hex': f"0x{int(input_addr):08x}",
                'input_space': input_space,
                'output_addr': int(output_addr),
                'output_hex': f"0x{int(output_addr):08x}",
                'output_space': output_space,
                'masks': masks,
                'line_num': i+1
            })
            i += 5
            continue
        
        i += 1
    
    print(f"✅ 解析完成: NOC操作 {len(noc_operations)}, VVV操作 {len(vvv_operations)}")
    
    # 生成分析报告
    with open(output_file, 'w', encoding='utf-8') as f:
        write_analysis_report(f, noc_operations, vvv_operations)
    
    print(f"📊 分析报告已保存到: {output_file}")

def write_analysis_report(f, noc_operations, vvv_operations):
    """写入分析报告"""
    
    f.write("="*80 + "\n")
    f.write("NOC和VVV操作快速分析报告\n")
    f.write("="*80 + "\n")
    f.write(f"NOC操作总数: {len(noc_operations)}\n")
    f.write(f"VVV操作总数: {len(vvv_operations)}\n")
    f.write("="*80 + "\n\n")
    
    # NOC操作统计
    f.write("📡 NOC操作统计\n")
    f.write("-"*50 + "\n")
    
    src_ops = [op for op in noc_operations if op['type'] == 'src_drv']
    dest_ops = [op for op in noc_operations if op['type'] == 'dest_drv']
    
    f.write(f"SRC操作: {len(src_ops)}\n")
    f.write(f"DEST操作: {len(dest_ops)}\n\n")
    
    # 地址空间使用统计
    f.write("地址空间使用统计:\n")
    addr_counter = Counter(op['address_space'] for op in noc_operations)
    for space, count in addr_counter.most_common():
        f.write(f"  {space}: {count} 次\n")
    f.write("\n")
    
    # 通信模式分析
    f.write("通信模式分析:\n")
    communication_patterns = []
    
    for i in range(0, len(noc_operations), 2):
        if i + 1 < len(noc_operations):
            src_op = noc_operations[i]
            dest_op = noc_operations[i + 1]
            
            if src_op['type'] == 'src_drv' and dest_op['type'] == 'dest_drv':
                pattern = f"G{src_op['dest_group']}C{src_op['dest_core']} ← G{dest_op['src_group']}C{dest_op['src_core']}"
                addr_pattern = f"{src_op['address_space']} → {dest_op['address_space']}"
                communication_patterns.append(f"{pattern} ({addr_pattern})")
    
    pattern_counter = Counter(communication_patterns)
    for pattern, count in pattern_counter.most_common(10):
        f.write(f"  {pattern}: {count} 次\n")
    f.write("\n")
    
    # VVV操作统计
    f.write("🧮 VVV操作统计\n")
    f.write("-"*50 + "\n")
    
    # VVV地址模式
    f.write("VVV地址模式:\n")
    vvv_patterns = [f"{op['input_space']} + {op['output_space']} → {op['output_space']}" 
                   for op in vvv_operations]
    vvv_counter = Counter(vvv_patterns)
    for pattern, count in vvv_counter.most_common():
        f.write(f"  {pattern}: {count} 次\n")
    f.write("\n")
    
    # 详细操作列表
    f.write("📋 详细操作列表 (前20个)\n")
    f.write("-"*80 + "\n")
    
    all_ops = []
    for op in noc_operations:
        all_ops.append(('NOC', op['line_num'], op))
    for op in vvv_operations:
        all_ops.append(('VVV', op['line_num'], op))
    
    all_ops.sort(key=lambda x: x[1])  # 按行号排序
    
    for i, (op_type, line_num, op) in enumerate(all_ops[:20]):
        f.write(f"[{i+1:2d}] Line {line_num:4d} | {op_type} | ")
        
        if op_type == 'NOC':
            if op['type'] == 'src_drv':
                f.write(f"SRC: {op['address_hex']} → G{op['dest_group']}C{op['dest_core']} ({op['address_space']})\n")
            else:
                f.write(f"DEST: G{op['src_group']}C{op['src_core']} → {op['address_hex']} ({op['address_space']})\n")
        else:
            f.write(f"VVV: {op['input_hex']} + {op['output_hex']} → {op['output_hex']}\n")
            f.write(f"     ({op['input_space']} + {op['output_space']} → {op['output_space']})\n")
    
    if len(all_ops) > 20:
        f.write(f"... (还有 {len(all_ops) - 20} 个操作)\n")
    
    f.write("\n" + "="*80 + "\n")

def main():
    """主函数"""
    import sys
    
    log_file = "python/logs/npu_demo_output.log"
    output_file = "noc_analysis.log"
    
    if len(sys.argv) > 1:
        log_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    if not Path(log_file).exists():
        print(f"❌ 日志文件不存在: {log_file}")
        return
    
    analyze_noc_vvv_log(log_file, output_file)

if __name__ == "__main__":
    main()
