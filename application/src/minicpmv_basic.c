#include <stdint.h>
#include <stdbool.h>

#include "minicpmv_basic.h"
#include "hardware_inst_data.h"
#include "software_port_data.h"
#include "primitive.h"
#include "high_level.h"
#include "minicpmv_def.h"
   

// 辅助函数: 从块张量中派生出视图 (dim1=rows)
void make_row_tensor(const Tensor *src_block, uint32_t start_row, uint32_t rows, Tensor *dst_row){
    *dst_row = *src_block;                                            // 复制元信息
    dst_row->base_addr += start_row * src_block->byte_stride1_u;      // 偏移到行
    dst_row->dim1       = rows;                                       // 行数
}

void make_tensor_view(const Tensor *src_tensor,
                      uint32_t start_row,
                      uint32_t start_col,
                      uint32_t rows,
                      uint32_t cols,
                      Tensor *dst_view)
{
    // Copy all metadata from the source tensor
    *dst_view = *src_tensor;

    // Calculate the new base address
    // Offset by rows using the byte_stride1
    // Offset by columns using the element width
    uint32_t bytes_per_element = src_tensor->width / 8;
    dst_view->base_addr += (start_row * src_tensor->byte_stride1_u) + (start_col * bytes_per_element);

    // Set the new dimensions of the view
    dst_view->dim1 = rows;
    dst_view->dim0 = cols;
}

// 根据 core_idx 生成只激活单核的 npu_mask
void make_single_core_mask(uint32_t core_idx, int* mask){
    for(int g=0; g<4; ++g) mask[g]=0;
    uint32_t group = core_idx / 4;      // 0~3
    uint32_t pos   = core_idx % 4;      // bit 0~3
    mask[group] = 1u << pos;
}

const static uint32_t core_cord[MINICPMV2_NUM_NODES] = {
    0x0000, 0x0001, 0x0002, 0x0003, 0x0100, 0x0101, 0x0102, 0x0103, 
    0x0200, 0x0201, 0x0202, 0x0203, 0x0300, 0x0301, 0x0302, 0x0303
};

const static uint32_t loop[MINICPMV2_NUM_NODES] = {
    0,  1,  2,  3,  4,  5,  6,  7, 15, 14, 13, 12, 11, 10,  9,  8
};

// for(int i = 0; i < MINICPMV2_NUM_NODES; i++) {
//     uint32_t node_index = loop[i];
//     core_rank[node_index] = i;
// }
// for(int i = 0; i < MINICPMV2_NUM_NODES; i++) {
//     src_cord[i] = core_cord[loop[(core_rank[i] - 1 + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES]];
//     dst_cord[i] = core_cord[loop[(core_rank[i] + 1 + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES]];
// }
const static uint32_t core_rank[MINICPMV2_NUM_NODES] = {0, 1, 2, 3, 4, 5, 6, 7, 15, 14, 13, 12, 11, 10, 9, 8};
const static uint32_t src_cord[MINICPMV2_NUM_NODES] = {0x0200, 0x0000, 0x0001, 0x0002, 0x0003, 0x0100, 0x0101, 0x0102, 0x0201, 0x0202, 0x0203, 0x0300, 0x0301, 0x0302, 0x0303, 0x0103};
const static uint32_t dst_cord[MINICPMV2_NUM_NODES] = {0x0001, 0x0002, 0x0003, 0x0100, 0x0101, 0x0102, 0x0103, 0x0303, 0x0000, 0x0200, 0x0201, 0x0202, 0x0203, 0x0300, 0x0301, 0x0302};



void AllGather(const Tensor* input1, const Tensor* input2) {
    debug_assert(input1->dim1 == MINICPMV2_NUM_NODES/2);

    uint32_t size_dim0a = 256 >> input1->width; // 256 bit
    uint32_t size_dim0b = (input1->dim0 + size_dim0a - 1) / size_dim0a; // dim0b = ceil(dim0 / size_dim0a)

    Tensor shape_tensor = {
        .base_addr = -1,
        .dim0 = input1->dim0,
        .dim1 = 1,
        .dim2 = 1,
        .byte_stride1_u = size_dim0b * 32,
        .byte_stride2_u = size_dim0b * 32,
        .width = input1->width,
        .type = input1->type
    };

    noc_primitive_cfg(&shape_tensor, (int[]){0xf, 0xf, 0xf, 0xf});

    for(int stage = 0; stage < MINICPMV2_NUM_NODES - 1; stage++) { // num_nodes - 1 stages
        uint32_t send_addr[MINICPMV2_NUM_NODES];
        uint32_t recv_addr[MINICPMV2_NUM_NODES];
        for(int i = 0; i < MINICPMV2_NUM_NODES; i++) { // every core send and recv
            // noc send line: core_rank[i] - stage + 1 % NUM_NODES
            // noc recv line: core_rank[i] - stage     % NUM_NODES
            uint32_t send_index = (core_rank[i] - stage + 1 + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES;
            uint32_t recv_index = (core_rank[i] - stage     + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES;


            send_addr[i] = ((send_index % 2 == 0) ? input1->base_addr : input2->base_addr) + ((uint32_t)(send_index/2)) * (input1->byte_stride1_u);
            recv_addr[i] = ((recv_index % 2 == 0) ? input1->base_addr : input2->base_addr) + ((uint32_t)(recv_index/2)) * (input1->byte_stride1_u);

            uint32_t onehot = 0x1 << i;
            int mask[4] = {onehot & 0xf, (onehot >> 4) & 0xf, (onehot >> 8) & 0xf, (onehot >> 12) & 0xf};

            noc_primitive_src_drv(send_addr[i], dst_cord[i], mask);
            noc_primitive_dest_drv(recv_addr[i], src_cord[i], mask);
        }
    }
}

void ReduceScatter(const Tensor* input1, const Tensor* input2, InterMemoryArray *InterMemory) {
    debug_assert(input1->dim1 == MINICPMV2_NUM_NODES/2);

    uint32_t size_dim0a = 256 >> input1->width; // 256 bit
    uint32_t size_dim0b = (input1->dim0 + size_dim0a - 1) / size_dim0a; // dim0b = ceil(dim0 / size_dim0a)

    Tensor shape_tensor = {
        .base_addr = -1,
        .dim0 = input1->dim0,
        .dim1 = 1,
        .dim2 = 1,
        .byte_stride1_u = size_dim0b * 32,
        .byte_stride2_u = size_dim0b * 32,
        .width = input1->width,
        .type = input1->type
    };

    noc_primitive_cfg(&shape_tensor, (int[]){0xf, 0xf, 0xf, 0xf});


    VP_Option vp_option = {
        .special_case.disable0 = 0, 
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation      = OPERATION_ADD, 
        .scalar_in2     = 0
    };
    vv_v_primitive_cfg(
        &shape_tensor, &shape_tensor, &shape_tensor, 
        &vp_option, 
        (int[]){0xf, 0xf, 0xf, 0xf}
    );

    for(int stage = 0; stage < MINICPMV2_NUM_NODES - 1; stage++) { // num_nodes - 1 stages
        uint32_t send_addr[MINICPMV2_NUM_NODES];
        uint32_t update_addr[MINICPMV2_NUM_NODES];
        uint32_t recv_addr[MINICPMV2_NUM_NODES];
        for(int i = 0; i < MINICPMV2_NUM_NODES; i++) { // every core send and recv
            // noc send line: core_rank[i] - stage + 1 % NUM_NODES
            // noc recv line: core_rank[i] - stage     % NUM_NODES
            uint32_t send_index = (core_rank[i] - stage     + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES;
            uint32_t recv_index = (core_rank[i] - stage - 1 + MINICPMV2_NUM_NODES) % MINICPMV2_NUM_NODES;

            send_addr[i]   = ((send_index % 2 == 0) ? input1->base_addr : input2->base_addr) + ((uint32_t)(send_index/2)) * (input1->byte_stride1_u);
            update_addr[i] = ((recv_index % 2 == 0) ? input1->base_addr : input2->base_addr) + ((uint32_t)(recv_index/2)) * (input1->byte_stride1_u);
            recv_addr[i]   = ((stage&0x1) == (core_rank[i] & 0x1)) ? InterMemory->memory[1].base_addr : InterMemory->memory[0].base_addr;

            uint32_t onehot = 0x1 << i;
            int mask[4] = {onehot & 0xf, (onehot >> 4) & 0xf, (onehot >> 8) & 0xf, (onehot >> 12) & 0xf};

            noc_primitive_src_drv(send_addr[i], dst_cord[i], mask);
            noc_primitive_dest_drv(recv_addr[i], src_cord[i], mask);

            vv_v_primitive_pre(recv_addr[i], update_addr[i], mask);
            vv_v_primitive_drv(update_addr[i], mask);
        }
    }

}


#define RMSNORM_EPS 1e-6f

void DistributedRMSNorm(const Tensor* input, const Tensor* output, const Tensor* norm_weight) {
    const uint32_t NUM_CORES      = 16;
    const uint32_t ROWS_PER_BATCH = NUM_CORES;        // 每 batch 16 行
    const uint32_t HALF_ROWS      = NUM_CORES / 2;    // 8 行
    const uint32_t FULL_IDX       = 1;
    uint32_t batch_num = input->dim1 / ROWS_PER_BATCH;

    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0
    const uint32_t SPAD1_BASE = 0x00100000u;  // Scratchpad1
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2
    const uint32_t SPAD3_BASE = 0x00300000u;  // Scratchpad3

    uint32_t row_bytes   = input->byte_stride1_u;            // 已为字节数
    uint32_t slice_bytes = HALF_ROWS * row_bytes;            // 8 行

    int FULL_MASK[4] = {0xf, 0xf, 0xf, 0xf};
    int ONE_MASK[4];
    Tensor in1, in2;
    Tensor row_P; 


    VP_Option vp_option = {
        .special_case.disable0 = 0,
        .special_case.round_mode = 0,
        .special_case.saturate = 0,
        .operation = OPERATION_MUL,
        .scalar_in2 = 0
    };

    InterMemory  im_norm_mem[2];
    InterMemoryArray im_norm = { .memory = im_norm_mem, .length = 2 };

    // int npu_mask[4] = {0xf, 0xf, 0xf, 0xf};
    for (uint32_t batch = 0; batch < batch_num; batch++) {
        uint32_t row_start = batch * ROWS_PER_BATCH;
        slice_to_spad(input,   row_start + 0,  HALF_ROWS, SPAD0_BASE, FULL_MASK, &in1);
        slice_to_spad(input,   row_start + 8,  HALF_ROWS, SPAD1_BASE, FULL_MASK, &in2);

        for(int core_id = 0; core_id < NUM_CORES; core_id++) {

            uint32_t full_row = (core_rank[core_id]+FULL_IDX) % NUM_CORES;
            uint32_t row_local= full_row/2;

            Tensor *P_block = (full_row%2==0) ? &in1 : &in2;


            // 3.2 构造单行视图
            make_row_tensor(P_block, row_local, 1, &row_P);


            make_single_core_mask(core_id, ONE_MASK);

            uint32_t base_tmp = P_block->base_addr + slice_bytes;   // in1 or in2 尾端
            im_norm.memory[0].base_addr = base_tmp;        // X²
            im_norm.memory[0].byte_size = row_bytes;
            im_norm.memory[1].base_addr = base_tmp + row_bytes; // 1/√mean
            im_norm.memory[1].byte_size = row_bytes;

            rmsnorm(&row_P, &row_P, (Tensor*)norm_weight, &vp_option, &im_norm, ONE_MASK);

            
        }

        AllGather(&in1, &in2);

        slice_to_ddr(&in1, row_start + 0, HALF_ROWS, output, FULL_MASK);
        slice_to_ddr(&in2, row_start + 8, HALF_ROWS, output, FULL_MASK);


    }  }



void slice_to_spad(const Tensor *tensor_gmem,
    uint32_t     start_row,   
    uint32_t     rows,        
    uint32_t     spad_base,
    int         *npu_mask,
    Tensor      *dst_local)
{
    //---------------- 源 DDR Slice ----------------
    Tensor src = *tensor_gmem;
    src.base_addr += start_row * tensor_gmem->byte_stride1_u; // byte_stride 已是字节单位
    src.dim1       = rows;              
    // dim0, dim2, stride 保持不变

    //---------------- 目的 SPAD Slice -------------
    Tensor dst = src;
    dst.base_addr = spad_base;

    //---------------- 触发搬运 --------------------
    load(&src, &dst, npu_mask);

    *dst_local = dst;                   // 返回本地视图
}

// 新增: 将SPAD切片写回DDR
void slice_to_ddr(const Tensor *tensor_spad,
    uint32_t     start_row,
    uint32_t     rows,        // 与 tensor_spad->dim1 一致
    const Tensor *tensor_gmem,
    int         *npu_mask)
{
    //-------------- 目标 DDR Slice --------------
    Tensor dst = *tensor_gmem;
    dst.base_addr += start_row * tensor_gmem->byte_stride1_u; // 字节单位
    dst.dim1       = rows;    // 写回行数

    //-------------- 写回 -------------------------
    store((Tensor *)tensor_spad, &dst, npu_mask);
}

void DistributedAddRMSNormAllReduce(const Tensor* input, const Tensor* input_x, const Tensor* output, const Tensor* output_norm, const Tensor* norm_weight, InterMemoryArray *dummy)
{
    //==== 常量定义 ====
    const uint32_t NUM_CORES      = 16;
    const uint32_t ROWS_PER_BATCH = NUM_CORES;        // 每 batch 16 行
    const uint32_t HALF_ROWS      = NUM_CORES / 2;    // 8 行
    const uint32_t FULL_IDX       = 1;
    // Scratchpad 起始基址
    const uint32_t SPAD0_BASE = 0x00000000u;  // Scratchpad0
    const uint32_t SPAD1_BASE = 0x00100000u;  // Scratchpad1
    const uint32_t SPAD2_BASE = 0x00200000u;  // Scratchpad2
    const uint32_t SPAD3_BASE = 0x00300000u;  // Scratchpad3

    // -------- 计算行与切片大小 --------
    uint32_t row_bytes   = input->byte_stride1_u;            // 已为字节数
    uint32_t slice_bytes = HALF_ROWS * row_bytes;            // 8 行

    // -------- npu mask --------
    int FULL_MASK[4] = {0xf, 0xf, 0xf, 0xf};
    int ONE_MASK[4];

    // -------- batch 数 --------
    uint32_t batch_num = input->dim1 / ROWS_PER_BATCH;       // dim1 = 总行数 N

    // -------- 局部张量句柄 --------
    Tensor in1, in2;      // ReduceScatter + 后续计算 (位于 SPAD0, SPAD1)
    Tensor x1,  x2;       // Residual 输入 (位于 SPAD2, SPAD3)

    Tensor row_P;         // 单行视图 (部分和 / ResAdd / Norm)
    Tensor row_X;         // 单行视图 (Residual)

    // -------- 局部 InterMemory 结构 --------
    InterMemory  im_rs_mem[2];
    InterMemoryArray im_rs = { .memory = im_rs_mem, .length = 2 };

    InterMemory  im_norm_mem[2];
    InterMemoryArray im_norm = { .memory = im_norm_mem, .length = 2 };


    for(uint32_t b = 0; b < batch_num; ++b){
        uint32_t row_start = b * ROWS_PER_BATCH;   // 当前 batch 首行号

        // --- DDR → SPAD : 输入 P & X ---
        slice_to_spad(input,   row_start + 0,  HALF_ROWS, SPAD0_BASE, FULL_MASK, &in1);
        slice_to_spad(input,   row_start + 8,  HALF_ROWS, SPAD1_BASE, FULL_MASK, &in2);
        slice_to_spad(input_x, row_start + 0,  HALF_ROWS, SPAD2_BASE, FULL_MASK, &x1);
        slice_to_spad(input_x, row_start + 8,  HALF_ROWS, SPAD3_BASE, FULL_MASK, &x2);

        // ---  ReduceScatter (使用专用 InterMemory) ---
        im_rs.memory[0].base_addr = SPAD0_BASE + slice_bytes; // 与 in1 同 SPAD
        im_rs.memory[0].byte_size = row_bytes;               // 1 行
        im_rs.memory[1].base_addr = SPAD1_BASE + slice_bytes; // 与 in2 同 SPAD
        im_rs.memory[1].byte_size = row_bytes;               // 1 行
        ReduceScatter(&in1, &in2, &im_rs);


        // --------  遍历 16 个逻辑核 --------
        VP_Option vp_add = {.special_case={0}, .operation = OPERATION_ADD, .scalar_in2 = 0};
        VP_Option vp_mul = {.special_case={0}, .operation = OPERATION_MUL, .scalar_in2 = 0};

        for(uint32_t core_i = 0; core_i < NUM_CORES; ++core_i){
            // 3.1 找到该逻辑核负责的完整行 (full_idx=0)
            // uint32_t full_row = (core_i + FULL_IDX) % NUM_CORES;
            uint32_t full_row = (core_rank[core_i]+FULL_IDX) % NUM_CORES;
            uint32_t row_local= full_row/2;

            Tensor *P_block = (full_row%2==0) ? &in1 : &in2;
            Tensor *X_block = (full_row%2==0) ? &x1  : &x2;

            // 3.2 构造单行视图
            make_row_tensor(P_block, row_local, 1, &row_P);
            make_row_tensor(X_block, row_local, 1, &row_X);

            // 3.3 构造只激活 core_i 的掩码
            make_single_core_mask(core_i, ONE_MASK);

            // 4) Residual Add
            add(&row_P, &row_X, &row_P, &vp_add, ONE_MASK);
            // 写回 DDR ,写回行号为batch_idx(b);因为npu_core循环中是写入不同npu_core的同一行；
            slice_to_ddr(&row_P, b, 1, output, ONE_MASK);
            // 5) RMSNorm (InterMemory 重新指向行缓冲)
            uint32_t base_tmp = P_block->base_addr + slice_bytes;   // in1 or in2 尾端
            im_norm.memory[0].base_addr = base_tmp;        // X²
            im_norm.memory[0].byte_size = row_bytes;
            im_norm.memory[1].base_addr = base_tmp + row_bytes; // 1/√mean
            im_norm.memory[1].byte_size = row_bytes;

            rmsnorm(&row_P, &row_P, (Tensor*)norm_weight, &vp_mul, &im_norm, ONE_MASK);
        }



        // --- ⑦ AllGather ---
        AllGather(&in1, &in2);

        // 写回DDR
        slice_to_ddr(&in1, row_start + 0, HALF_ROWS, output_norm, FULL_MASK);
        slice_to_ddr(&in2, row_start + 8, HALF_ROWS, output_norm, FULL_MASK);
    }
}